import os
import json
import pandas as pd
from masks_process_http import latest_table_path, check_department
from utils1 import create_downloadurl
def process_monthly_meeting_report(file_path):
    """
    处理 xx部月例会报表.xlsx 文件，提取指定列并校验责任部门字段

    参数:
        file_path (str): 文件路径

    返回:
        pd.DataFrame: 处理后的数据
    """
    # 读取Excel文件，先读取全部内容
    raw_df = pd.read_excel(file_path, header=None)

    # 删除首行开始连续的空白行
    while raw_df.iloc[0].isnull().all():
        raw_df = raw_df.iloc[1:].reset_index(drop=True)

    # 删除首列开始连续的空白列
    while raw_df.iloc[:, 0].isnull().all():
        raw_df = raw_df.iloc[:, 1:].reset_index(drop=True)

    # 跳过第一列，第二行作为表头
    df = raw_df.iloc[2:].reset_index(drop=True)

    df.columns = raw_df.iloc[1]

    # 清理表头中的换行符和前后空白
    raw_df.columns = [str(col).replace('\n', '').strip() for col in raw_df.columns]

    # 提取所需列
    # required_columns = ["序号", "任务名称", "任务开展情况", "任务类别", "责任部门"]
    required_columns = ["序号", "任务名称", "任务开展情况", "责任部门"]
    extracted_df = df[required_columns]

    # 校验责任部门字段
    validated_df = extracted_df.apply(check_department, axis=1)
    # 将责任部门字段出现次数最多的值作为该文件的责任部门
    if not validated_df.empty:
        main_dept = validated_df['责任部门'].mode()[0]
        # validated_df['责任部门'] = main_dept


    extracted_dict = extracted_df[['序号', '任务名称', '任务开展情况']].to_dict(orient='records')
    

    return main_dept, extracted_dict

def extract_middle_by_dept(middle_path, month, department):
    df_middle = pd.read_excel(middle_path)
    # 计算季度
    q = (month - 1) // 3 + 1
    progress_col = f'{q}季度进度目标'
    deliver_col = f'{q}季度交付物'
    associa_progress_col = f'{q}季度关联进度目标'
    associa_deliver_col = f'{q}季度关联交付物'
    # 按部门分组提取
    group = df_middle[df_middle['责任部门'] == department]
    records = []
    for _, row in group.iterrows():
        if row['完成状态'] == "未完成":
            records.append({
                '序号': row['序号'],
                '任务': row['任务'],
                '1级任务子项': row['1级任务子项'],
                '2级任务子项': row['2级任务子项'],
                '季度进度目标': row.get(progress_col, ""),
                '季度关联进度目标': row.get(associa_progress_col, ""),
                '季度交付物': row.get(deliver_col, ""),
                '季度关联交付物': row.get(associa_deliver_col, ""),
                '最新进展': row['最新进展']
            })
    return records

def extract_task_progress(report_paths, month):
    with open(latest_table_path, 'r') as f:
        middle_table_path = json.load(f)[0]
    data = []
    for report_path in report_paths:
        if not os.path.exists(report_path):
            return {'error': f'月报文件 {report_path} 不存在。'}
        dept, extracted_dict = process_monthly_meeting_report(report_path)
        task_df = extract_middle_by_dept(middle_table_path, month, dept)
        data.append({'month': month, 'dept': dept, 'monthly_data': extracted_dict, 'middle_data': task_df})
    return {'result': data}

def fill_progress_in_middle_table(tasks_progress_list, month, department):
    source_name = f"{str(month)}月{department}月例会报表"
    
    with open(latest_table_path, 'r') as f:
        middle_table_path = json.load(f)[0]
    if os.path.exists(middle_table_path.replace('.xlsx', '_filled.xlsx')):
        middle_table_path = middle_table_path.replace('.xlsx', '_filled.xlsx')
        output_path = middle_table_path
    else:
        output_path = middle_table_path.replace('.xlsx', '_filled.xlsx')
    df = pd.read_excel(middle_table_path)
    annual_idx_map = {str(row['序号']): idx for idx, row in df.iterrows() if row['序号'] <= 66}
    for item in tasks_progress_list:
        if '中间表序号' in item:
            seq = str(int(item['中间表序号']))
            idx = annual_idx_map.get(seq)
            if idx is not None:
                latest_progress = item['最新进展'].replace('\n', '')
                if item.get('月报序号'):
                    df.at[idx, '最新进展'] = f"最新进展：{latest_progress}（任务来源：{source_name}-{str(item['月报序号'])}）\n未完成交付物：{item['未完成交付物']}"
                else:
                    df.at[idx, '最新进展'] = f"最新进展：{latest_progress}\n未完成交付物：{item['未完成交付物']}"
    df.to_excel(output_path, index=False)
    # 获取uuid
    file_path = os.path.abspath(output_path)
    dir_path = os.path.dirname(file_path)
    uuid = os.path.basename(dir_path)
    file_name = os.path.basename(output_path)
    download_url = create_downloadurl(uuid, file_name)
    return f'message: 已完成《{source_name}》与中间表的关联!下载链接: {download_url}'
