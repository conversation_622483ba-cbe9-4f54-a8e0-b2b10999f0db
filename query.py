import json
import pandas as pd
import os
import shutil
# from split_data import build_parent_child_structure
from data_process_2 import check_department, latest_table_path

# --------------------------------- 未使用 -------------------------------------
# def process_query(report_path, output_path="completion_info_split_data.json"):
#     """
#     获取任务数据所需的字段。

#     返回:
#         list: 任务数据所需的字段列表。
#     """
#     report = pd.read_excel(report_path, header=1)
#     # 调用函数处理合并后的任务表
#     config = {
#         "fields": ["任务名称", "任务开展情况"],
#         "parent_max_tokens": 4000,
#         "child_max_tokens": 30000
#     }
#     structured_data = build_parent_child_structure(report, config)
#     with open(output_path, 'w', encoding='utf-8') as f:
#         json.dump(structured_data, f, ensure_ascii=False, indent=2)

#     print(f"处理完成，共生成 {len(structured_data)} 条记录，已保存至 {output_path}")

# ---------------------------------------------------------------------------------

def process_monthly_meeting_report(file_path):
    """
    处理 xx部月例会报表.xlsx 文件，提取指定列并校验责任部门字段

    参数:
        file_path (str): 文件路径

    返回:
        pd.DataFrame: 处理后的数据
    """
    # 读取Excel文件，先读取全部内容
    raw_df = pd.read_excel(file_path, header=None)
    
    # 删除首行开始连续的空白行
    while raw_df.iloc[0].isnull().all():
        raw_df = raw_df.iloc[1:].reset_index(drop=True)

    # 删除首列开始连续的空白列
    while raw_df.iloc[:, 0].isnull().all():
        raw_df = raw_df.iloc[:, 1:].reset_index(drop=True)

    # 跳过第一列，第二行作为表头
    df = raw_df.iloc[2:].reset_index(drop=True)
    df.columns = raw_df.iloc[1]

    # 提取所需列
    required_columns = ["序号", "任务名称", "任务开展情况", "任务类别", "责任部门"]
    extracted_df = df[required_columns]

    # 校验责任部门字段
    validated_df = extracted_df.apply(check_department, axis=1)

    return validated_df

def append_new_reports_to_merged(merged_path="reports/merged_report.xlsx", new_reports_dir="reports/new_reports", previous_reports_dir="reports/previous_reports"):
    """
    将 new_reports 文件夹下的所有文件添加到 merged_report.xlsx 中，
    添加后将这些文件移动到 previous_reports 文件夹。
    """
    # 确保 previous_reports 文件夹存在
    os.makedirs(previous_reports_dir, exist_ok=True)
    # 获取 new_reports 文件夹下所有 Excel 文件
    files = [f for f in os.listdir(new_reports_dir) if f.endswith('.xlsx')]
    if not files:
        print("new_reports 文件夹下没有需要添加的文件。")
        return

    # 读取已合并的表
    if os.path.exists(merged_path):
        merged_df = pd.read_excel(merged_path)
    else:
        # 如果没有已合并的表，则用第一个新文件创建
        first_file_path = os.path.join(new_reports_dir, files[0])
        merged_df = pd.read_excel(first_file_path, header=1)
        # 移动第一个文件到 previous_reports
        shutil.move(first_file_path, os.path.join(previous_reports_dir, files[0]))
        print(f"已创建新合并表并移动文件: {files[0]}")
        files = files[1:]  # 剩余文件继续追加

    # 依次追加新文件
    for file in files:
        file_path = os.path.join(new_reports_dir, file)
        new_df = pd.read_excel(file_path, header=2)
        merged_df = pd.concat([merged_df, new_df], ignore_index=True)
        # 移动文件到 previous_reports
        shutil.move(file_path, os.path.join(previous_reports_dir, file))
        print(f"已添加并移动文件: {file}")

    # 保存合并后的表
    merged_df.to_excel(merged_path, index=False)
    print(f"所有新文件已添加到 {merged_path}，并移动到 {previous_reports_dir}")

def extract_and_combine_for_compare(merged_path="reports/merged_report.xlsx", task_path="middle_table/任务目标清单.xlsx"):
    # 读取合并后的月报
    merged_df = pd.read_excel(merged_path)
    merged_fields = merged_df[['任务名称', '任务开展情况', '牵头部门']]

    # 读取任务目标清单
    task_df = pd.read_excel(task_path)
    task_fields = task_df[['任务', '1级任务子项', '2级任务子项', '任务目标', '责任部门']]

    # 按部门进行组合
    combined = []
    for _, m_row in merged_fields.iterrows():
        dept = m_row['牵头部门']
        # 找到责任部门相同的任务目标清单行
        matched_tasks = task_fields[task_fields['责任部门'] == dept]
        for _, t_row in matched_tasks.iterrows():
            combined.append({
                '牵头部门': dept,
                '任务名称': m_row['任务名称'],
                '任务开展情况': m_row['任务开展情况'],
                '任务': t_row['任务'],
                '1级任务子项': t_row['1级任务子项'],
                '2级任务子项': t_row['2级任务子项'],
                '任务目标': t_row['任务目标'],
                '责任部门': t_row['责任部门']
            })
    # 转为DataFrame
    combined_df = pd.DataFrame(combined)
    return combined_df

def extract_task_progress(report_path):
    """
    处理月报和任务目标清单，生成合并后的报告。
    """
    # 处理月报
    monthly_report_df = process_monthly_meeting_report(report_path)

    with open(latest_table_path, 'r') as f:
        middle_table_path = json.load(f)[0]

    # # 提取任务目标清单
    # task_df = pd.read_excel(middle_table_path)

    # 合并数据
    combined_df = extract_and_combine_for_compare(
        merged_path=report_path,
        task_path=middle_table_path
    )

    return combined_df