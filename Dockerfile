# 使用官方 Python 镜像作为基础镜像
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

## 替换系统源为阿里云
#RUN sed -i 's/http.debian.net/mirrors.aliyun.com/g' /etc/apt/sources.list && \
#    sed -i 's/https:\/\/deb.debian.org\/debian\//http:\/\/mirrors.aliyun.com\/debian\//g' /etc/apt/sources.list

## 替换系统源为阿里云
RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources

# 安装 Ghostscript
RUN apt-get update && \
    apt-get install -y --no-install-recommends ghostscript vim && \
    rm -rf /var/lib/apt/lists/*


# 复制当前目录下的所有文件到容器中的 /app 目录
COPY . .

# 安装依赖（如果有的话）
RUN pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 暴露应用运行的端口
EXPOSE 5002

# 启动命令，运行 Flask 应用
CMD ["python", "startAPI.py"]