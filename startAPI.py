from flask import Flask,request

import Config
import mask_api
# import missing_inspect
# from exts import db
import logging

app = Flask(__name__)
app.config.from_object(Config)  # 加载数据库配置文件
# db.init_app(app)  # 绑定到我们到应用程序
# 注册user，使用前缀 user 作为前缀访问
# app.register_blueprint(missing_inspect.blueprint)
app.register_blueprint(mask_api.blueprint)

handler = logging.StreamHandler()
handler.setLevel(logging.DEBUG)  # 设置日志记录最低级别为DEBUG，低于DEBUG级别的日志记录会被忽略，不设置setLevel()则默认为NOTSET级别。
logging_format = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s')
handler.setFormatter(logging_format)
app.logger.addHandler(handler)


@app.before_request
def log_request_info():
    try:
    # 打印请求信息
        app.logger.warning(f"Request Method: {request.method}")
        app.logger.warning(f"Request URL: {request.url}")
        app.logger.warning(f"Query Params: {request.args}")
        app.logger.warning(f"Headers: {request.headers}")
        app.logger.warning(f"Remote IP: {request.remote_addr}")

        # 如果请求携带 JSON 数据，可以解析
   
        app.logger.warning(f"JSON Data: {request.get_json()}")
    except Exception as e:
        app.logger.error(e)
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002)
