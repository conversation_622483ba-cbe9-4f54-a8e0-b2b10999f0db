"""
国网湖南信通公司工作计划管理系统主应用
"""
import os
import logging
from flask import Flask, request
from pathlib import Path

from app.config import config
from app.api.mask_api import blueprint as mask_api_blueprint

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 确保必要的目录存在
    ensure_directories(app.config)
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    app.register_blueprint(mask_api_blueprint)
    
    # 注册请求日志中间件
    @app.before_request
    def log_request_info():
        try:
            app.logger.info(f"Request Method: {request.method}")
            app.logger.info(f"Request URL: {request.url}")
            app.logger.info(f"Query Params: {request.args}")
            app.logger.info(f"Remote IP: {request.remote_addr}")
            
            # 记录JSON数据（如果有）
            json_data = request.get_json()
            if json_data:
                app.logger.info(f"JSON Data: {json_data}")
        except Exception as e:
            app.logger.error(f"Error logging request info: {e}")
    
    return app

def ensure_directories(config):
    """确保必要的目录存在"""
    directories = [
        config.get('DATA_DIR'),
        config.get('INPUT_DIR'),
        config.get('PROCESSED_DIR'),
        config.get('OUTPUT_DIR'),
        config.get('MIDDLE_TABLES_DIR'),
        config.get('TEMP_DIR'),
        config.get('PDF_DIR'),
        config.get('LOG_DIR'),
    ]
    
    for directory in directories:
        if directory:
            Path(directory).mkdir(parents=True, exist_ok=True)

def setup_logging(app):
    """配置日志"""
    if not app.debug:
        # 文件日志处理器
        log_dir = Path(app.config.get('LOG_DIR', 'logs'))
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_dir / 'app.log')
        file_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        
        # 控制台日志处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG if app.debug else logging.INFO)
        
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        app.logger.addHandler(file_handler)
        app.logger.addHandler(console_handler)
        app.logger.setLevel(logging.DEBUG if app.debug else logging.INFO)

if __name__ == '__main__':
    app = create_app()
    app.run(
        host=app.config.get('API_HOST', '0.0.0.0'),
        port=app.config.get('API_PORT', 5002),
        debug=app.config.get('DEBUG', False)
    )
