"""
国网湖南信通公司工作计划管理系统配置文件
"""
import os
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).parent.parent.resolve()

class Config:
    """基础配置类"""
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 数据目录配置
    DATA_DIR = BASE_DIR / 'data'
    INPUT_DIR = DATA_DIR / 'input' / 'monthly_plans'
    PROCESSED_DIR = DATA_DIR / 'processed'
    OUTPUT_DIR = DATA_DIR / 'output'
    MIDDLE_TABLES_DIR = OUTPUT_DIR / 'middle_tables'
    TEMP_DIR = DATA_DIR / 'temp'
    PDF_DIR = TEMP_DIR / 'pdf'
    
    # 日志配置
    LOG_DIR = BASE_DIR / 'logs'
    LOG_LEVEL = 'INFO'
    
    # API配置
    API_HOST = '0.0.0.0'
    API_PORT = 5002
    
    # 文件路径配置
    MAPPING_TABLE_PATH = TEMP_DIR / 'monthly_mapping_table.json'
    LATEST_TABLE_PATH = TEMP_DIR / 'latest_table_path.txt'
    IP_FILE_PATH = BASE_DIR / 'ip.txt'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
