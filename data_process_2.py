import os
import re
import pandas as pd
import json
from difflib import get_close_matches
# from match import BgeM3Retriever
# from datetime import datetime
from pathlib import Path
from datetime import datetime
from mcp.types import TextContent

# 获取当前文件的父目录绝对路径
parent_dir = Path(__file__).parent.resolve()
monthly_dir = os.path.join(parent_dir, 'data/monthly_plan')
processed_monthly_dir = os.path.join(parent_dir, 'data/processed_monthly_plan')
mapping_table_path = os.path.join(parent_dir, 'tmp_dir/monthly_mapping_table.json')
latest_table_path = os.path.join(parent_dir, 'tmp_dir/latest_table_path.txt')
# 定义任务中间表字段
cols_needed = [
    '序号', '原表序号', '任务', '1级任务子项', '2级任务子项',
    '1季度进度目标', '1季度交付物', '1季度关联任务目标', '1季度关联交付物',
    '2季度进度目标', '2季度交付物', '2季度关联任务目标', '2季度关联交付物',
    '3季度进度目标', '3季度交付物', '3季度关联任务目标', '3季度关联交付物',
    '4季度进度目标', '4季度交付物', '4季度关联任务目标', '4季度关联交付物',
    '责任部门', '配合部门', '责任人', '任务来源', '完成状态', '最新进展'
]

# 定义合法部门列表
valid_departments = [
    "安全监察部", "技术发展部", "调度监控中心", "通信技术中心",
    "安全防护中心", "应用运维中心", "平台技术中心", "数据运营中心",
    "项目管理中心", "数管中心", "科技研发中心", "财务资产部",
    "党委党建部", "综合管理部"
]

def validate_department(dept):
    """
    验证部门名称是否合法，若不合法则尝试匹配最接近的部门。

    参数:
        dept (str): 输入的部门名称

    返回:
        tuple: (有效部门名或None, 建议部门)
    """
    if dept in valid_departments:
        return dept, None
    else:
        # 简单模糊匹配：查找包含dept字符串的有效部门
        possible_matches = [d for d in valid_departments if dept in d]
        if not possible_matches:
            # 如果没有匹配项，使用difflib进行近似匹配
            possible_matches = get_close_matches(dept, valid_departments, n=1, cutoff=0.6)
        return None, possible_matches[0] if possible_matches else "未找到匹配部门"


def check_department(row):
    """
    校验每一行的责任部门字段

    参数:
        row (pd.Series): DataFrame 中的一行数据

    返回:
        pd.Series: 校正后的行数据
    """
    valid_dept, suggestion = validate_department(row["责任部门"])
    if valid_dept is None:
        print(f"警告: 部门 '{row['责任部门']}' 不在合法部门列表中！建议: {suggestion}")
        row["责任部门"] = suggestion  # 自动替换为建议的部门
    return row

#  ----- 年度重点工作任务处理 -----
def extract_annual_source_from_excel(file_path):
    """
    从年度Excel文件的第1行（索引为0）读取任务来源名称。

    参数:
        file_path (str): 年度Excel文件路径

    返回:
        str: 提取的“x年重点工作任务清单”字符串
    """
    # 只读取前两行用于获取标题行
    df_title = pd.read_excel(file_path, nrows=1, header=None)
    if not df_title.empty and 0 in df_title:
        title_value = df_title.iloc[0, 0]  # 获取第一列第一个单元格内容
        return str(title_value).strip()
    return "年度重点工作计划（未知年份）"

def process_task_data(input_file_path):
    """
    处理年度重点任务清单Excel文件并输出统一格式表格。
    """
    # 读取任务来源名称
    source_name = extract_annual_source_from_excel(input_file_path)
    print(f"提取的任务来源名称: {source_name}")
    # 读取Excel文件，并跳过第一行
    df_annual = pd.read_excel(input_file_path, skiprows=1)

    # 提取需要的列
    columns_to_extract = [
        '序号', '重点工作', '子项任务', '年度总体任务目标',
        '1季度进度目标', '1季度交付物',
        '2季度进度目标', '2季度交付物',
        '3季度进度目标', '3季度交付物',
        '4季度进度目标', '4季度交付物',
        '责任部门', '协同部门', '责任人'
    ]
    df_annual = df_annual[columns_to_extract]

    # # 创建新的“任务目标”列
    # def combine_quarters(row):
    #     result = []
    #     quarters = ['1季度', '2季度', '3季度', '4季度']
    #     for quarter in quarters:
    #         progress_col = f'{quarter}进度目标'
    #         deliverable_col = f'{quarter}交付物'
    #         if pd.notna(row[progress_col]) or pd.notna(row[deliverable_col]):
    #             time = quarter
    #             progress = row[progress_col] if pd.notna(row[progress_col]) else ''
    #             deliverable = row[deliverable_col] if pd.notna(row[deliverable_col]) else ''
    #             result.append(f"{time}:\n\t进度目标:{progress}\n\t交付物：{deliverable}")
    #     return '\n'.join(result)

    # df['任务目标'] = df.apply(combine_quarters, axis=1)

    # # 删除原有的季度列
    # quarter_columns = [
    #     '1季度进度目标', '1季度交付物',
    #     '2季度进度目标', '2季度交付物',
    #     '3季度进度目标', '3季度交付物',
    #     '4季度进度目标', '4季度交付物'
    # ]
    # df.drop(columns=quarter_columns, inplace=True)

    # 新增各季度关联任务目标和交付物列
    df_annual['1季度关联任务目标'] = ''
    df_annual['1季度关联交付物'] = ''
    df_annual['2季度关联任务目标'] = ''
    df_annual['2季度关联交付物'] = ''
    df_annual['3季度关联任务目标'] = ''
    df_annual['3季度关联交付物'] = ''
    df_annual['4季度关联任务目标'] = ''
    df_annual['4季度关联交付物'] = ''

    # 新增“任务来源”列
    df_annual['任务来源'] = source_name

    # 新增“完成情况”和“最新进展”列
    df_annual['完成状态'] = '未完成'
    df_annual['最新进展'] = ''

    # 列重命名
    df_annual.rename(columns={'重点工作': '任务'}, inplace=True)
    df_annual.rename(columns={'子项任务': '1级任务子项'}, inplace=True)
    df_annual.rename(columns={'年度总体任务目标': '2级任务子项'}, inplace=True)
    df_annual.rename(columns={'协同部门': '配合部门'}, inplace=True)
    df_annual.rename(columns={'序号': '原表序号'}, inplace=True)
    df_annual.insert(0, '序号', range(1, len(df_annual) + 1))
    df_annual['序号'] = df_annual['序号'].astype(int)
    # 保留并排序字段
    df_annual = df_annual.reindex(columns=cols_needed) # type: ignore


    return df_annual
# ---------------------------------------------------------------------------

# ----- 月度工作计划处理 -----
def extract_monthly_source_from_excel(file_path):
    """
    从月度Excel文件的第2行（索引为1）读取任务来源名称。

    参数:
        file_path (str): 月度Excel文件路径

    返回:
        str: 提取的“x年x月月度重点工作计划”字符串
    """
    # 只读取前两行用于获取标题行
    df_title = pd.read_excel(file_path, nrows=2, header=None)
    if not df_title.empty and 1 in df_title:
        title_value = df_title.iloc[1, 0]  # 获取第二行第一列对应单元格的内容
        return str(title_value).strip()
    return "月度重点工作计划（未知月份）"

def filter_monthly_by_annual(df_monthly, annual_tasks, retriever, threshold=0.5):
    """
    按责任部门过滤月度任务，只与同部门的年度任务比对。
    用月度计划中的任务、任务目标去比对年度重点工作任务中的任务、1级任务子项、2级任务子项、任务目标。
    """
    keep_indices = []
    for idx, row in df_monthly.iterrows():
        # 校验部门
        valid_dept, suggestion = validate_department(row['责任部门'])
        dept = valid_dept if valid_dept else suggestion
        row['责任部门'] = dept

        # 只与同部门的年度任务比对
        annual_dept_tasks = annual_tasks[annual_tasks['责任部门'] == dept]
        if annual_dept_tasks.empty:
            keep_indices.append(idx)
            continue

        # 构造比对文本
        monthly_texts = [str(row['任务']), str(row['任务目标'])]
        annual_texts = []
        for _, a_row in annual_dept_tasks.iterrows():
            annual_texts.extend([
                str(a_row['任务']),
                str(a_row['1级任务子项']),
                str(a_row['2级任务子项']),
                str(a_row['任务目标'])
            ])

        # 向量化并混合检索
        query_vecs = retriever.vectorize(monthly_texts, mode='combined')
        doc_vecs = retriever.vectorize(annual_texts, mode='combined')
        scores = retriever.combined_similarity(query_vecs, doc_vecs)['combined'].flatten()
        max_score = scores.max()
        if max_score < threshold:
            keep_indices.append(idx)
        else:
            print(f"月度任务“{row['任务']}”已被年度任务包含（部门：{dept}，相似度 {max_score:.2f}），跳过。")
    return df_monthly.loc[keep_indices].reset_index(drop=True)

def append_monthly_data(df_base, monthly_file_path, annual_tasks=None, retriever=None):
    """
    将月度计划数据追加到基础DataFrame中。

    参数:
        df_base (pd.DataFrame): 已有的统一格式DataFrame。
        monthly_file_path (str): 月度计划Excel文件路径。

    返回:
        pd.DataFrame: 合并后的新DataFrame
    """
    # 自动提取任务来源名称（从月度文件第2行读取）
    source_name = extract_monthly_source_from_excel(monthly_file_path)
    print(f"提取的任务来源名称: {source_name}")
    # 读取月度计划，从第3行开始作为表头
    df_monthly = pd.read_excel(monthly_file_path, header=2)
    # 清理表头中的换行符和前后空白
    df_monthly.columns = [str(col).replace('\n', '').strip() for col in df_monthly.columns]
    # # 过滤掉分类行（假设分类行是只有“序号”有内容，其他都为空）
    # df_monthly = df_monthly[df_monthly['事项'].notna()]

    # --- 新增预处理 ---
    re_first = re.compile(r'^[一二三四五六七八九十]+、')
    re_second = re.compile(r'^（[一二三四五六七八九十]+）、')

    first_cat = ""
    second_cat = ""
    work_categories = []

    for idx, row in df_monthly.iterrows():
        seq = str(row['序号']).strip()
        flag = pd.isna(row['事项'])
        if flag:
            # 一级分类
            if re_first.match(seq):
                # 去掉前缀，只保留具体类别
                first_cat = re_first.sub('', seq)
                second_cat = ""
                work_categories.append(None)
                continue
            # 二级分类
            elif re_second.match(seq):
                second_cat = re_second.sub('', seq)
                work_categories.append(None)
                continue
            else:
                work_categories.append(None)
        # 普通数据行
        else:
            if first_cat:
                if second_cat:
                    work_categories.append(f"{first_cat} - {second_cat}")
                else:
                    work_categories.append(f"{first_cat}")
            else:
                work_categories.append("")


    # 只保留数据行（事项不为空），并加上“工作类别”字段
    df_monthly = df_monthly[df_monthly['事项'].notna()].copy()
    df_monthly['工作类别'] = [cat for cat in work_categories if cat is not None]

    # 按部门分组提取
    result = {}
    for dept, group in df_monthly.groupby('牵头部门'):
        result[dept] = group[['序号', '事项', '计划安排时间', '工作类别']].to_dict(orient='records')

    # 字段映射
    df_monthly['任务'] = df_monthly['事项']
    df_monthly['责任部门'] = df_monthly['牵头部门']
    df_monthly['配合部门'] = df_monthly['协办部门']
    df_monthly['任务目标'] = df_monthly['计划安排时间']

    # 校验责任部门字段
    df_monthly = df_monthly.apply(check_department, axis=1)

    # ==== 按部门比对年度任务，过滤已包含项 ====
    if annual_tasks is not None and retriever is not None:
        df_monthly = filter_monthly_by_annual(df_monthly, annual_tasks, retriever)

    # 添加固定字段
    # 新增各季度关联任务目标和交付物列
    df_monthly['1季度关联任务目标'] = ''
    df_monthly['1季度关联交付物'] = ''
    df_monthly['2季度关联任务目标'] = ''
    df_monthly['2季度关联交付物'] = ''
    df_monthly['3季度关联任务目标'] = ''
    df_monthly['3季度关联交付物'] = ''
    df_monthly['4季度关联任务目标'] = ''
    df_monthly['4季度关联交付物'] = ''
    df_monthly['任务来源'] = source_name
    df_monthly['完成状态'] = '未完成'
    df_monthly['最新进展'] = ''
    
    # 列重排序
    df_monthly = df_monthly.reindex(columns=cols_needed) # type: ignore

    # 拼接数据
    df_combined = pd.concat([df_base, df_monthly], ignore_index=True)

    return df_combined

def parse_year_month(source_name):
    # 支持“2025年3月月度重点工作计划”或“2025年03月月度重点工作计划”等格式
    match = re.search(r'(\d{4})[年\-](\d{1,2})[月]', source_name)
    if match:
        year = int(match.group(1))
        month = int(match.group(2))
        return year, month
    return 0, 0

import pandas as pd

def month_to_quarters(month):
    """根据月份返回上一季度、该季度和下一季度的序号（1~4）"""
    q = (month - 1) // 3 + 1
    return [q]
    prev_q = q - 1 if q > 1 else 1
    next_q = q + 1 if q < 4 else 4
    return [prev_q, q, next_q]

def extract_and_combine_monthly_annual(monthly_path, annual_path, month):
    # 读取月度计划
    df_monthly = pd.read_excel(monthly_path, header=2)
    # 清理表头中的换行符和前后空白
    df_monthly.columns = [str(col).replace('\n', '').strip() for col in df_monthly.columns]
    # 过滤掉分类行（假设分类行是只有“序号”有内容，其他都为空）
    df_monthly = df_monthly[df_monthly['事项'].notna()]

    monthly_fields = df_monthly[['事项', '计划安排时间', '牵头部门']]

    # 读取年度重点任务
    df_annual = pd.read_excel(annual_path, skiprows=1)
    annual_fields = df_annual[
        ['重点工作', '子项任务', '年度总体任务目标',
         '1季度进度目标', '1季度交付物',
         '2季度进度目标', '2季度交付物',
         '3季度进度目标', '3季度交付物',
         '4季度进度目标', '4季度交付物',
         '责任部门']
    ]

    # 获取相关季度
    quarters = month_to_quarters(month)
    quarter_info = []
    for q in quarters:
        quarter_info.append((f"{q}季度进度目标", f"{q}季度交付物"))

    # 按部门组合
    combined = []
    for _, m_row in monthly_fields.iterrows():
        dept = m_row['牵头部门']
        matched_annual = annual_fields[annual_fields['责任部门'] == dept]
        for _, a_row in matched_annual.iterrows():
            # 提取季度任务目标和交付物
            quarter_targets = []
            for progress_col, deliver_col in quarter_info:
                progress = a_row.get(progress_col, "")
                deliver = a_row.get(deliver_col, "")
                quarter_targets.append(f"{progress_col}:{progress}, {deliver_col}:{deliver}")
            combined.append({
                '牵头部门': dept,
                '事项': m_row['事项'],
                '计划安排时间': m_row['计划安排时间'],
                '重点工作': a_row['重点工作'],
                '子项任务': a_row['子项任务'],
                '年度总体任务目标': a_row['年度总体任务目标'],
                '季度任务目标及交付物': "; ".join(quarter_targets),
                '责任部门': a_row['责任部门']
            })
    return combined
    # return pd.DataFrame(combined)

def extract_monthly_by_dept(monthly_path, monthly_output_path):
    df_monthly = pd.read_excel(monthly_path, header=2)
    df_monthly.columns = [str(col).replace('\n', '').strip() for col in df_monthly.columns]
    # df_monthly = df_monthly[df_monthly['事项'].notna()]

    # --- 新增预处理 ---
    re_first = re.compile(r'^[一二三四五六七八九十]+、')
    re_second = re.compile(r'^（[一二三四五六七八九十]+）')

    first_cat = ""
    second_cat = ""
    work_categories = []

    for idx, row in df_monthly.iterrows():
        seq = str(row['序号']).strip()
        flag = pd.isna(row['事项'])
        if flag:
            # 一级分类
            if re_first.match(seq):
                # 去掉前缀，只保留具体类别
                first_cat = re_first.sub('', seq)
                second_cat = ""
                work_categories.append(None)
            # 二级分类
            elif re_second.match(seq):
                second_cat = re_second.sub('', seq)
                work_categories.append(None)
            else:
                work_categories.append(None)
        # 普通数据行
        else:
            if first_cat:
                if second_cat:
                    work_categories.append(f"{first_cat} - {second_cat} - {seq}")
                else:
                    work_categories.append(f"{first_cat} - {seq}")
            else:
                work_categories.append("")


    # 只保留数据行（事项不为空），并修改“序号”字段
    df_monthly = df_monthly[df_monthly['事项'].notna()].copy()
    df_monthly['序号'] = [cat for cat in work_categories if cat is not None]
    df_monthly.rename(columns={'牵头部门': '责任部门'}, inplace=True)
    df_monthly = df_monthly.apply(check_department, axis=1)

    df_monthly.to_excel(monthly_output_path, index=False)
    # 按部门分组提取
    result = {}
    for dept, group in df_monthly.groupby('责任部门'):
        result[dept] = group[['序号', '事项', '计划安排时间']].to_dict(orient='records')
    return result

def extract_annual_by_dept(annual_path, month, departments):
    df_annual = pd.read_excel(annual_path, skiprows=1)
    # 计算季度
    q = (month - 1) // 3 + 1
    progress_col = f'{q}季度进度目标'
    deliver_col = f'{q}季度交付物'
    # 按部门分组提取
    result = {}
    for dept, group in df_annual.groupby('责任部门'):
        if dept in departments:
            records = []
            for _, row in group.iterrows():
                records.append({
                    '序号': row['序号'] if '序号' in row else None,
                    '重点工作': row['重点工作'],
                    '子项任务': row['子项任务'],
                    '年度总体任务目标': row['年度总体任务目标'],
                    '季度任务目标': row.get(progress_col, ""),
                    '季度交付物': row.get(deliver_col, "")
                })
            result[dept] = records
    return result

def extract_all_monthly_and_annual(annual_path):
    monthly_files = [os.path.join(d, f) for d in os.listdir(monthly_dir) for f in os.listdir(os.path.join(monthly_dir, d)) if f.endswith('.xlsx')]
    if not monthly_files:
        print("没有找到月度计划文件。")
        return
    data = []
    record = {}
    for monthly_file in monthly_files:
        monthly_file_path = os.path.join(monthly_dir, monthly_file)
        source_name = extract_monthly_source_from_excel(monthly_file_path)
        print(f"提取的任务来源名称: {source_name}")
        year, month = parse_year_month(source_name)
        if year == 0 or month == 0:
            print(f"文件 {monthly_file} 未能识别年月，跳过。")
            continue
        monthly_output_path = os.path.join(processed_monthly_dir, source_name + '.xlsx')
        # 月度数据
        mdata = extract_monthly_by_dept(monthly_file_path, monthly_output_path)
        departments = list(mdata.keys())
        # 年度数据
        annual_data = extract_annual_by_dept(annual_path, month, departments)
        data.append({'source_name': source_name, 'year': year, 'month': month, 'monthly_data': mdata, 'annual_data': annual_data})
        record[source_name] = monthly_output_path
        os.remove(monthly_file_path)

    # 将字典保存到文件
    if os.path.exists(mapping_table_path):
        with open(mapping_table_path, 'r', encoding='utf-8') as file:
            saved_record = json.load(file)
        record.update(saved_record)
    os.makedirs(os.path.dirname(mapping_table_path), exist_ok=True)
    with open(mapping_table_path, 'w', encoding='utf-8') as file:
        json.dump(record, file, ensure_ascii=False, indent=4)
    return data

def main():
    from datetime import datetime
    formatted_now = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    annual_input_path = os.path.join(parent_dir, 'data/annual_tasks/国网湖南信通公司2025年重点工作任务清单.xlsx')
    monthly_input_path = os.path.join(parent_dir, 'data/monthly_plan')
    output_path = os.path.join(parent_dir, rf'middle_table/任务目标清单_{formatted_now}.xlsx')
    combined_df = process_task_data(annual_input_path)
    # 输出最终结果
    combined_df.to_excel(output_path, index=False)
    # monthly_files = [f for f in os.listdir(monthly_input_path) if f.endswith('.xlsx')]
    # if not monthly_files:
    #     print("没有找到月度计划文件。")
    #     return

    # for monthly_file in monthly_files:
    #     monthly_file_path = os.path.join(monthly_input_path, monthly_file)
    #     source_name = extract_monthly_source_from_excel(monthly_file_path)
    #     year, month = parse_year_month(source_name)
    #     if year == 0 or month == 0:
    #         print(f"文件 {monthly_file} 未能识别年月，跳过。")
    #         continue

    #     # 月度数据
    #     monthly_result = extract_monthly_by_dept(monthly_file_path)
    #     monthly_json_path = os.path.join(
    #         parent_dir,
    #         f"middle_table/monthly_by_dept_{year}_{month:02d}.json"
    #     )
    #     with open(monthly_json_path, "w", encoding="utf-8") as f:
    #         json.dump(monthly_result, f, ensure_ascii=False, indent=4)
    #     print(f"月度数据已保存至: {monthly_json_path}")

    #     # 年度数据
    #     annual_result = extract_annual_by_dept(annual_input_path, month)
    #     annual_json_path = os.path.join(
    #         parent_dir,
    #         f"middle_table/annual_by_dept_{year}_{month:02d}.json"
    #     )
    #     with open(annual_json_path, "w", encoding="utf-8") as f:
    #         json.dump(annual_result, f, ensure_ascii=False, indent=4)
    #     print(f"年度数据已保存至: {annual_json_path}")

def annual_data_process(annual_input_path):
    # 输出文件路径
    middle_table_name = 'middle_table.xlsx'
    formatted_now = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    middle_table_output_path_root = os.path.join(parent_dir, rf'middle_table/{formatted_now}')
    if not os.path.exists(middle_table_output_path_root):
        os.makedirs(middle_table_output_path_root)

    if os.path.exists(annual_input_path):
        combined_df = process_task_data(annual_input_path)
    else:
        return [TextContent(type="text", text=f"年度重点工作任务文件不存在，请上传！")]
    
    middle_table_output_path = os.path.join(middle_table_output_path_root, middle_table_name)
    combined_df.to_excel(middle_table_output_path, index=False)

    if not os.path.exists(os.path.dirname(latest_table_path)):
        os.makedirs(os.path.dirname(latest_table_path), exist_ok=True)
    with open(latest_table_path, 'w') as f:
        json.dump([middle_table_output_path, annual_input_path], f)
    return [TextContent(type="text", text=f"年度重点工作任务已成功更新到中间表中")]

def monthly_data_extract():
    if not os.path.exists(processed_monthly_dir):
        os.makedirs(processed_monthly_dir)

    with open(latest_table_path, 'r') as f:
        latest_annual_tasks_path = json.load(f)[1]

    data = extract_all_monthly_and_annual(latest_annual_tasks_path)
    return [TextContent(type='text', text=json.dumps(data, ensure_ascii=False, indent=4))]

def fill_associations(assoc_list, month, source_name):
    with open(latest_table_path, 'r') as f:
        middle_table_path = json.load(f)[0]
    with open(mapping_table_path, 'r') as f:
        monthly_plan_path = json.load(f)[source_name]

    quarter = month_to_quarters(month)[0]
    
    # 读取中间表
    df = pd.read_excel(middle_table_path)
    # 读取月度计划表
    df_monthly = pd.read_excel(monthly_plan_path)
    if '原表序号' in df.columns:
        df['原表序号'] = df['原表序号'].astype(str)
    else:
        return [TextContent(type="text", text=f"error:新表缺少原表序号列")]
    annual_idx_map = {str(row['原表序号']): idx for idx, row in df.iterrows() if row['原表序号'].isdigit() and int(row['原表序号']) <= 66}

    # 关联填充
    for item in assoc_list:
        if '年度重点工作任务序号' in item:
            seq = str(item['年度重点工作任务序号'])
            idx = annual_idx_map.get(seq)
            if idx is not None:
                col_target = f"{quarter}季度关联任务目标"
                col_deliver = f"{quarter}季度关联交付物"
                # 只填充当前季度（可根据实际需求调整）
                if item.get('关联任务目标'):
                    old_target_val = df.at[idx, col_target]
                    if pd.isna(old_target_val):
                        old_target_val = ''
                    else:
                        old_target_val = str(old_target_val)
                    cur_target_id = len(old_target_val.split('\n'))
                    assoc_target= item['关联任务目标'].replace('\n', '')
                    df.at[idx, col_target] = old_target_val + f"{cur_target_id}.{assoc_target}（任务来源：{source_name}；关联理由：{item.get('关联理由','')}）\n"
                if item.get('关联交付物'):
                    old_deliver_val = df.at[idx, col_deliver]
                    if pd.isna(old_deliver_val):
                        old_deliver_val = ''
                    else:
                        old_deliver_val = str(old_deliver_val)
                    cur_deliver_id = len(old_deliver_val.split('\n'))
                    assoc_deliver = item['关联交付物'].replace('\n', '')
                    df.at[idx, col_deliver] = old_deliver_val + f"{cur_deliver_id}.{assoc_deliver} 时间节点：{item.get('时间节点要求','')}（任务来源：{source_name}；关联理由：{item.get('关联理由','')}）\n"
        # 非关联数据，按source_name和月度计划序号追加
        elif '月度计划序号' in item:
            seq = item['月度计划序号']
            # 查找月度计划表唯一序号对应的数据
            rec = df_monthly[df_monthly['序号'] == seq]
            if not rec.empty:
                max_serial = df['序号'].max() if '序号' in df.columns else 0
                new_row = {col: '' for col in df.columns}
                new_row['序号'] = max_serial + 1
                new_row['原表序号'] = rec.iloc[0]['序号']
                new_row['任务'] = rec.iloc[0]['事项']
                new_row[f'{quarter}季度进度目标'] = rec.iloc[0]['计划安排时间']
                new_row['任务来源'] = source_name
                new_row['责任部门'] = rec.iloc[0]['牵头部门'] if '牵头部门' in rec.columns and not pd.isna(rec.iloc[0]['牵头部门']) else ''
                new_row['配合部门'] = rec.iloc[0]['协办部门'] if '协办部门' in rec.columns and not pd.isna(rec.iloc[0]['协办部门']) else ''
                new_row['完成状态'] = '未完成'
                # 其他字段可按需补充
                df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
    # 重新编号
    df = df.reset_index(drop=True)
    df['序号'] = range(1, len(df) + 1)

    # 保存
    # output_path = middle_table_path.replace('.xlsx', '_filled.xlsx')
    df.to_excel(middle_table_path, index=False)
    return [TextContent(type="text", text=f"已完成《{source_name}》与中间表的关联!")]

if __name__ == "__main__":
    main()