import uuid
from datetime import datetime

import uuid
from datetime import datetime
import os
import requests

# 生成简化的uuid字符串用于文件名
def get_uuid_name():
    # 获取精确到微秒的时间戳字符串（16位）
    timestamp_str = datetime.now().strftime("%Y%m%d%H%M%S%f")
    # 取UUID的前8位
    short_uuid = str(uuid.uuid4())[:8]
    # 拼接时间戳和短UUID
    unique_id = f"{timestamp_str}{short_uuid}"
    return unique_id


# 从指定url获取文件,并将文件使用get_uuid_name()更改名称后保存在pdf目录下
def get_file_from_url(url: str):
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    uuid_str=get_uuid_name()
    # 构建pdf目录路径
    pdf_dir = os.path.join(current_dir, 'pdf',uuid_str)

    # 如果pdf目录不存在，则创建
    if not os.path.exists(pdf_dir):
        os.makedirs(pdf_dir)

    # 发送HTTP GET请求下载文件
    response = requests.get(url)

    # 获取原始文件扩展名
    ext = os.path.splitext(url)[1]
    # 使用get_uuid_name()生成新文件名，并保留原始扩展名
    unique_filename = uuid_str + ext
    # 构建文件保存路径
    file_path = os.path.join(pdf_dir, unique_filename)

    # 写入文件
    with open(file_path, 'wb') as f:
        f.write(response.content)

    print(f"文件已保存至：{file_path}")
    return file_path, uuid_str


if __name__ == '__main__':
    str1, str2=get_file_from_url("http://fuduji1024.com:8083/test.pdf")
    print(str1, str2)